package com.longbridge.flink.udf;

import org.junit.Test;
import static org.junit.Assert.*;

public class CryptoUDFTest {

    @Test
    public void testEncryptDecrypt() {
        EncryptFunction encryptFunction = new EncryptFunction();
        DecryptFunction decryptFunction = new DecryptFunction();
        
        String original = "hello world";
        String encrypted = encryptFunction.eval(original);
        String decrypted = decryptFunction.eval(encrypted);
        
        assertNotNull(encrypted);
        assertNotEquals(original, encrypted);
        assertEquals(original, decrypted);
    }

    @Test
    public void testEncryptDecryptWithCustomKey() {
        EncryptFunction encryptFunction = new EncryptFunction();
        DecryptFunction decryptFunction = new DecryptFunction();
        
        String original = "test message";
        String customKey = "1234567890123456";
        String encrypted = encryptFunction.eval(original, customKey);
        String decrypted = decryptFunction.eval(encrypted, customKey);
        
        assertNotNull(encrypted);
        assertNotEquals(original, encrypted);
        assertEquals(original, decrypted);
    }

    @Test
    public void testXorEncodeDecode() {
        XorEncodeFunction encodeFunction = new XorEncodeFunction();
        XorDecodeFunction decodeFunction = new XorDecodeFunction();
        
        String original = "test data";
        String encoded = encodeFunction.eval(original);
        String decoded = decodeFunction.eval(encoded);
        
        assertNotNull(encoded);
        assertEquals(original, decoded);
    }

    @Test
    public void testNullInput() {
        EncryptFunction encryptFunction = new EncryptFunction();
        DecryptFunction decryptFunction = new DecryptFunction();
        XorEncodeFunction encodeFunction = new XorEncodeFunction();
        XorDecodeFunction decodeFunction = new XorDecodeFunction();
        
        assertEquals("", encryptFunction.eval(null));
        assertEquals("", decryptFunction.eval(null));
        assertNull(encodeFunction.eval(null));
        assertNull(decodeFunction.eval(null));
    }

    @Test
    public void testEmptyString() {
        EncryptFunction encryptFunction = new EncryptFunction();
        DecryptFunction decryptFunction = new DecryptFunction();
        
        String empty = "";
        String encrypted = encryptFunction.eval(empty);
        String decrypted = decryptFunction.eval(encrypted);
        
        assertEquals("", encrypted);
        assertEquals("", decrypted);
    }
}