package com.longbridge.flink.udf.example;

import com.longbridge.flink.udf.DecryptFunction;
import com.longbridge.flink.udf.EncryptFunction;
import com.longbridge.flink.udf.XorDecodeFunction;
import com.longbridge.flink.udf.XorEncodeFunction;

/**
 * 简单的加密解密功能测试，不需要启动 Flink 环境
 */
public class SimpleCryptoTest {
    
    public static void main(String[] args) {
        System.out.println("=== Flink Crypto UDF 功能测试 ===\n");
        
        // 测试数据
        String testData = "Hello, Longbridge!";
        String customKey = "1234567890123456"; // 16字节密钥
        
        // 创建 UDF 实例
        EncryptFunction encryptFunction = new EncryptFunction();
        DecryptFunction decryptFunction = new DecryptFunction();
        XorEncodeFunction xorEncodeFunction = new XorEncodeFunction();
        XorDecodeFunction xorDecodeFunction = new XorDecodeFunction();
        
        System.out.println("原始数据: " + testData);
        System.out.println();
        
        // 测试 AES 加密/解密（默认密钥）
        System.out.println("=== AES 加密/解密测试（默认密钥） ===");
        try {
            String encrypted = encryptFunction.eval(testData);
            String decrypted = decryptFunction.eval(encrypted);
            
            System.out.println("加密后: " + encrypted);
            System.out.println("解密后: " + decrypted);
            System.out.println("测试结果: " + (testData.equals(decrypted) ? "✅ 通过" : "❌ 失败"));
        } catch (Exception e) {
            System.out.println("❌ AES 测试失败: " + e.getMessage());
        }
        System.out.println();
        
        // 测试 AES 加密/解密（自定义密钥）
        System.out.println("=== AES 加密/解密测试（自定义密钥） ===");
        try {
            String encryptedCustom = encryptFunction.eval(testData, customKey);
            String decryptedCustom = decryptFunction.eval(encryptedCustom, customKey);
            
            System.out.println("自定义密钥: " + customKey);
            System.out.println("加密后: " + encryptedCustom);
            System.out.println("解密后: " + decryptedCustom);
            System.out.println("测试结果: " + (testData.equals(decryptedCustom) ? "✅ 通过" : "❌ 失败"));
        } catch (Exception e) {
            System.out.println("❌ AES 自定义密钥测试失败: " + e.getMessage());
        }
        System.out.println();
        
        // 测试 XOR 编码/解码
        System.out.println("=== XOR 编码/解码测试 ===");
        try {
            String encoded = xorEncodeFunction.eval(testData);
            String decoded = xorDecodeFunction.eval(encoded);
            
            System.out.println("编码后: " + encoded);
            System.out.println("解码后: " + decoded);
            System.out.println("测试结果: " + (testData.equals(decoded) ? "✅ 通过" : "❌ 失败"));
        } catch (Exception e) {
            System.out.println("❌ XOR 测试失败: " + e.getMessage());
        }
        System.out.println();
        
        // 测试空值处理
        System.out.println("=== 空值处理测试 ===");
        try {
            String nullEncrypt = encryptFunction.eval(null);
            String nullDecrypt = decryptFunction.eval(null);
            String nullEncode = xorEncodeFunction.eval(null);
            String nullDecode = xorDecodeFunction.eval(null);
            
            System.out.println("null 加密结果: " + nullEncrypt);
            System.out.println("null 解密结果: " + nullDecrypt);
            System.out.println("null 编码结果: " + nullEncode);
            System.out.println("null 解码结果: " + nullDecode);
            System.out.println("测试结果: ✅ 空值处理正常");
        } catch (Exception e) {
            System.out.println("❌ 空值处理测试失败: " + e.getMessage());
        }
        System.out.println();
        
        // 测试空字符串处理
        System.out.println("=== 空字符串处理测试 ===");
        try {
            String emptyEncrypt = encryptFunction.eval("");
            String emptyDecrypt = decryptFunction.eval("");
            
            System.out.println("空字符串加密结果: '" + emptyEncrypt + "'");
            System.out.println("空字符串解密结果: '" + emptyDecrypt + "'");
            System.out.println("测试结果: ✅ 空字符串处理正常");
        } catch (Exception e) {
            System.out.println("❌ 空字符串处理测试失败: " + e.getMessage());
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
}
