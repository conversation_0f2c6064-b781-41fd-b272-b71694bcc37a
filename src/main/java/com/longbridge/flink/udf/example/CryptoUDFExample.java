package com.longbridge.flink.udf.example;

import com.longbridge.flink.udf.DecryptFunction;
import com.longbridge.flink.udf.EncryptFunction;
import com.longbridge.flink.udf.XorDecodeFunction;
import com.longbridge.flink.udf.XorEncodeFunction;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class CryptoUDFExample {
    
    public static void main(String[] args) {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);
        
        // Register UDF functions
        tableEnv.createTemporarySystemFunction("ENCRYPT", EncryptFunction.class);
        tableEnv.createTemporarySystemFunction("DECRYPT", DecryptFunction.class);
        tableEnv.createTemporarySystemFunction("XOR_ENCODE", XorEncodeFunction.class);
        tableEnv.createTemporarySystemFunction("XOR_DECODE", XorDecodeFunction.class);
        
        // Create a simple table for demonstration
        tableEnv.executeSql("CREATE TABLE source_table (" +
                "id INT, " +
                "message STRING" +
                ") WITH (" +
                "'connector' = 'datagen'," +
                "'rows-per-second' = '1'," +
                "'fields.id.min' = '1'," +
                "'fields.id.max' = '10'," +
                "'fields.message.length' = '20'" +
                ")");
        
        // Create sink table
        tableEnv.executeSql("CREATE TABLE sink_table (" +
                "id INT, " +
                "original_message STRING, " +
                "encrypted_message STRING, " +
                "decrypted_message STRING, " +
                "xor_encoded STRING, " +
                "xor_decoded STRING" +
                ") WITH (" +
                "'connector' = 'print'" +
                ")");
        
        // Execute the transformation with UDF functions
        tableEnv.executeSql("INSERT INTO sink_table " +
                "SELECT " +
                "id, " +
                "message as original_message, " +
                "ENCRYPT(message) as encrypted_message, " +
                "DECRYPT(ENCRYPT(message)) as decrypted_message, " +
                "XOR_ENCODE(message) as xor_encoded, " +
                "XOR_DECODE(XOR_ENCODE(message)) as xor_decoded " +
                "FROM source_table");
    }
}