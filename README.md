# Flink Crypto UDF

A Flink User-Defined Function (UDF) library for encryption and decryption operations.

## Features

- **AES Encryption/Decryption**: Secure AES-256 encryption with CBC mode and PKCS5 padding
- **XOR Encoding/Decoding**: Simple XOR-based encoding for lightweight obfuscation
- **Flexible Key Management**: Support for default and custom encryption keys
- **Flink Integration**: Ready-to-use UDF functions for Flink SQL

## Project Structure

```
flink-crypto-udf/
├── src/main/java/com/longbridge/flink/udf/
│   ├── utils/
│   │   └── CryptoUtil.java              # Core encryption utilities
│   ├── EncryptFunction.java             # AES encryption UDF
│   ├── DecryptFunction.java             # AES decryption UDF
│   ├── XorEncodeFunction.java           # XOR encoding UDF
│   ├── XorDecodeFunction.java           # XOR decoding UDF
│   └── example/
│       └── CryptoUDFExample.java        # Usage example
└── src/test/java/com/longbridge/flink/udf/
    └── CryptoUDFTest.java               # Unit tests
```

## Build

### 生产环境构建
```bash
mvn clean package
```

### 本地开发构建（包含 Flink 依赖）
```bash
mvn clean package -P local-dev
```

## 运行测试

### 运行单元测试
```bash
mvn test
```

### 运行功能测试（不需要 Flink 环境）
```bash
# 编译项目
mvn compile

# 运行 CryptoUtil 工具类测试
java -cp target/classes com.longbridge.flink.udf.example.CryptoUtilTest
```

## 问题解决

### NoClassDefFoundError 问题
如果遇到 `NoClassDefFoundError: org/apache/flink/streaming/api/environment/StreamExecutionEnvironment` 错误，这是因为 Flink 依赖项被设置为 `provided` scope。

**解决方案：**
1. 使用 `local-dev` profile 进行本地开发：
   ```bash
   mvn compile -P local-dev
   ```

2. 或者在 Flink 集群环境中运行，集群会提供这些依赖项。

### XOR 编码问题修复
修复了 XOR 编码在处理中文字符时的问题：
- 使用 Base64 编码处理 XOR 操作后的二进制数据
- 确保字符编码的一致性

## Usage

### 1. Register UDF Functions

```java
StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);

// Register functions
tableEnv.createTemporarySystemFunction("ENCRYPT", EncryptFunction.class);
tableEnv.createTemporarySystemFunction("DECRYPT", DecryptFunction.class);
tableEnv.createTemporarySystemFunction("XOR_ENCODE", XorEncodeFunction.class);
tableEnv.createTemporarySystemFunction("XOR_DECODE", XorDecodeFunction.class);
```

### 2. Use in SQL Queries

```sql
-- AES encryption with default key
SELECT ENCRYPT('sensitive data') as encrypted_data;

-- AES encryption with custom key
SELECT ENCRYPT('sensitive data', 'my_custom_key_16') as encrypted_data;

-- Decrypt data
SELECT DECRYPT('encrypted_base64_string') as decrypted_data;

-- XOR encoding/decoding
SELECT XOR_ENCODE('test data') as encoded_data;
SELECT XOR_DECODE('encoded_data') as decoded_data;
```

### 3. Complete Example

```sql
INSERT INTO processed_table
SELECT 
    id,
    original_field,
    ENCRYPT(original_field) as encrypted_field,
    DECRYPT(ENCRYPT(original_field)) as round_trip_test,
    XOR_ENCODE(original_field) as obfuscated_field
FROM source_table;
```

## Testing

Run unit tests:
```bash
mvn test
```

## Dependencies

- Java 11
- Apache Flink 1.20.0
- Flink CDC 3.2.1 (for CDC capabilities)

## Security Notes

- The default encryption key is hardcoded for demonstration purposes
- In production, use environment variables or secure key management systems
- XOR encoding provides minimal security and should only be used for obfuscation