import com.longbridge.flink.udf.utils.CryptoUtil;

public class TestCrypto {
    public static void main(String[] args) {
        String original = "Hello World";
        
        // Test XOR encoding/decoding
        String encoded = CryptoUtil.encode(original);
        String decoded = CryptoUtil.decode(encoded);
        
        System.out.println("Original: " + original);
        System.out.println("Encoded: " + encoded);
        System.out.println("Decoded: " + decoded);
        System.out.println("XOR Test " + (original.equals(decoded) ? "PASSED" : "FAILED"));
        
        // Test AES encryption/decryption
        String encrypted = CryptoUtil.encrypt(original);
        String decrypted = CryptoUtil.decrypt(encrypted);
        
        System.out.println("\nEncrypted: " + encrypted);
        System.out.println("Decrypted: " + decrypted);
        System.out.println("AES Test " + (original.equals(decrypted) ? "PASSED" : "FAILED"));
    }
}
