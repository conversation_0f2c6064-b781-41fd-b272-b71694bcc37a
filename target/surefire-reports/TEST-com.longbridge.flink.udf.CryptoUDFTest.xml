<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.longbridge.flink.udf.CryptoUDFTest" time="0.039" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="socksProxyHost" value="127.0.0.1"/>
    <property name="http.proxyHost" value="127.0.0.1"/>
    <property name="java.specification.version" value="23"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Longbridge/flink-crypto-udf/target/test-classes:/Users/<USER>/Longbridge/flink-crypto-udf/target/classes:/Users/<USER>/.m2/repository/org/apache/flink/flink-streaming-java/1.20.1/flink-streaming-java-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-core/1.20.1/flink-core-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-core-api/1.20.1/flink-core-api-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-metrics-core/1.20.1/flink-metrics-core-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-annotations/1.20.1/flink-annotations-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-shaded-asm-9/9.5-17.0/flink-shaded-asm-9-9.5-17.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-shaded-jackson/2.14.2-17.0/flink-shaded-jackson-2.14.2-17.0.jar:/Users/<USER>/.m2/repository/org/snakeyaml/snakeyaml-engine/2.6/snakeyaml-engine-2.6.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.10.0/commons-text-1.10.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/kryo/2.24.0/kryo-2.24.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/minlog/1.2/minlog-1.2.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/2.1/objenesis-2.1.jar:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.0/commons-compress-1.26.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-file-sink-common/1.20.1/flink-file-sink-common-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-runtime/1.20.1/flink-runtime-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-rpc-core/1.20.1/flink-rpc-core-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-rpc-akka-loader/1.20.1/flink-rpc-akka-loader-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-queryable-state-client-java/1.20.1/flink-queryable-state-client-java-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-hadoop-fs/1.20.1/flink-hadoop-fs-1.20.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.15.1/commons-io-2.15.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-shaded-netty/4.1.91.Final-17.0/flink-shaded-netty-4.1.91.Final-17.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-shaded-zookeeper-3/3.7.1-17.0/flink-shaded-zookeeper-3-3.7.1-17.0.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.24.0-GA/javassist-3.24.0-GA.jar:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.10.4/snappy-java-1.1.10.4.jar:/Users/<USER>/.m2/repository/tools/profiler/async-profiler/2.9/async-profiler-2.9.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-java/1.20.1/flink-java-1.20.1.jar:/Users/<USER>/.m2/repository/com/twitter/chill-java/0.7.6/chill-java-0.7.6.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-shaded-guava/31.1-jre-17.0/flink-shaded-guava-31.1-jre-17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-connector-datagen/1.20.1/flink-connector-datagen-1.20.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-api-java-bridge/1.20.1/flink-table-api-java-bridge-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-api-java/1.20.1/flink-table-api-java-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-api-bridge-base/1.20.1/flink-table-api-bridge-base-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-planner-loader/1.20.1/flink-table-planner-loader-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-clients/1.20.1/flink-clients-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-optimizer/1.20.1/flink-optimizer-1.20.1.jar:/Users/<USER>/.m2/repository/commons-cli/commons-cli/1.5.0/commons-cli-1.5.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-datastream/1.20.1/flink-datastream-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-datastream-api/1.20.1/flink-datastream-api-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-runtime/1.20.1/flink-table-runtime-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-common/1.20.1/flink-table-common-1.20.1.jar:/Users/<USER>/.m2/repository/com/ibm/icu/icu4j/67.1/icu4j-67.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-cep/1.20.1/flink-cep-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-cdc-base/3.2.1/flink-cdc-base-3.2.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-cdc-common/3.2.1/flink-cdc-common-3.2.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-cdc-runtime/3.2.1/flink-cdc-runtime-3.2.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-connector-mysql-cdc/3.2.1/flink-connector-mysql-cdc-3.2.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-connector-debezium/3.2.1/flink-connector-debezium-3.2.1.jar:/Users/<USER>/.m2/repository/io/debezium/debezium-api/1.9.8.Final/debezium-api-1.9.8.Final.jar:/Users/<USER>/.m2/repository/io/debezium/debezium-embedded/1.9.8.Final/debezium-embedded-1.9.8.Final.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-api/3.2.0/connect-api-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-clients/3.2.0/kafka-clients-3.2.0.jar:/Users/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-runtime/3.2.0/connect-runtime-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-transforms/3.2.0/connect-transforms-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-tools/3.2.0/kafka-tools-3.2.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/argparse4j/argparse4j/0.7.0/argparse4j-0.7.0.jar:/Users/<USER>/.m2/repository/ch/qos/reload4j/reload4j/1.2.19/reload4j-1.2.19.jar:/Users/<USER>/.m2/repository/org/bitbucket/b_c/jose4j/0.7.9/jose4j-0.7.9.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.12.6/jackson-annotations-2.12.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/jaxrs/jackson-jaxrs-json-provider/2.12.6/jackson-jaxrs-json-provider-2.12.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/jaxrs/jackson-jaxrs-base/2.12.6/jackson-jaxrs-base-2.12.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.12.6/jackson-module-jaxb-annotations-2.12.6.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.2/jakarta.xml.bind-api-2.3.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.1/jakarta.activation-api-1.2.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet/2.34/jersey-container-servlet-2.34.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet-core/2.34/jersey-container-servlet-core-2.34.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/external/jakarta.inject/2.6.1/jakarta.inject-2.6.1.jar:/Users/<USER>/.m2/repository/jakarta/ws/rs/jakarta.ws.rs-api/2.1.6/jakarta.ws.rs-api-2.1.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/inject/jersey-hk2/2.34/jersey-hk2-2.34.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-locator/2.6.1/hk2-locator-2.6.1.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/external/aopalliance-repackaged/2.6.1/aopalliance-repackaged-2.6.1.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-api/2.6.1/hk2-api-2.6.1.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-utils/2.6.1/hk2-utils-2.6.1.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1.1/activation-1.1.1.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.4.44.v20210927/jetty-server-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.4.44.v20210927/jetty-http-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.4.44.v20210927/jetty-io-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.4.44.v20210927/jetty-servlet-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.4.44.v20210927/jetty-security-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util-ajax/9.4.44.v20210927/jetty-util-ajax-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlets/9.4.44.v20210927/jetty-servlets-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-continuation/9.4.44.v20210927/jetty-continuation-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.4.44.v20210927/jetty-util-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/9.4.44.v20210927/jetty-client-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.12/reflections-0.9.12.jar:/Users/<USER>/.m2/repository/org/apache/maven/maven-artifact/3.8.4/maven-artifact-3.8.4.jar:/Users/<USER>/.m2/repository/org/codehaus/plexus/plexus-utils/3.3.0/plexus-utils-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-json/3.2.0/connect-json-3.2.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.12.6/jackson-datatype-jdk8-2.12.6.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-file/3.2.0/connect-file-3.2.0.jar:/Users/<USER>/.m2/repository/io/debezium/debezium-connector-mysql/1.9.8.Final/debezium-connector-mysql-1.9.8.Final.jar:/Users/<USER>/.m2/repository/io/debezium/debezium-core/1.9.8.Final/debezium-core-1.9.8.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.2/jackson-core-2.13.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.2.2/jackson-databind-2.13.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.2/jackson-datatype-jsr310-2.13.2.jar:/Users/<USER>/.m2/repository/io/debezium/debezium-ddl-parser/1.9.8.Final/debezium-ddl-parser-1.9.8.Final.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.8/antlr4-runtime-4.8.jar:/Users/<USER>/.m2/repository/com/zendesk/mysql-binlog-connector-java/0.27.2/mysql-binlog-connector-java-0.27.2.jar:/Users/<USER>/.m2/repository/com/github/luben/zstd-jni/1.5.0-2/zstd-jni-1.5.0-2.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar:/Users/<USER>/.m2/repository/com/esri/geometry/esri-geometry-api/2.2.0/esri-geometry-api-2.2.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-test-utils/1.20.1/flink-test-utils-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-test-utils-junit/1.20.1/flink-test-utils-junit-1.20.1.jar:/Users/<USER>/.m2/repository/org/junit/vintage/junit-vintage-engine/5.10.1/junit-vintage-engine-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.19.1/testcontainers-1.19.1.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.3.3/docker-java-api-3.3.3.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.3.3/docker-java-transport-zerodep-3.3.3.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.3.3/docker-java-transport-3.3.3.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.12.1/jna-5.12.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-runtime/1.20.1/flink-runtime-1.20.1-tests.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-core/1.20.1/flink-core-1.20.1-tests.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-rpc-akka-loader/1.20.1/flink-rpc-akka-loader-1.20.1-tests.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.23.1/assertj-core-3.23.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.12.10/byte-buddy-1.12.10.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-streaming-java/1.20.1/flink-streaming-java-1.20.1-tests.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-statebackend-rocksdb/1.20.1/flink-statebackend-rocksdb-1.20.1.jar:/Users/<USER>/.m2/repository/com/ververica/frocksdbjni/6.20.3-ververica-2.0/frocksdbjni-6.20.3-ververica-2.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-statebackend-changelog/1.20.1/flink-statebackend-changelog-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-statebackend-common/1.20.1/flink-statebackend-common-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-dstl-dfs/1.20.1/flink-dstl-dfs-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-test/5.4.0/curator-test-5.4.0.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.7.1/zookeeper-3.7.1.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper-jute/3.7.1/zookeeper-jute-3.7.1.jar:/Users/<USER>/.m2/repository/org/apache/yetus/audience-annotations/0.12.0/audience-annotations-0.12.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.76.Final/netty-handler-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.76.Final/netty-common-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.76.Final/netty-resolver-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.76.Final/netty-buffer-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.76.Final/netty-transport-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.76.Final/netty-codec-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.76.Final/netty-transport-native-epoll-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.76.Final/netty-transport-native-unix-common-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.76.Final/netty-transport-classes-epoll-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/3.2.5/metrics-core-3.2.5.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:"/>
    <property name="https.proxyPort" value="7890"/>
    <property name="java.vm.vendor" value="Homebrew"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="23"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Longbridge/flink-crypto-udf/target/surefire/surefirebooter-20250722155539682_3.jar /Users/<USER>/Longbridge/flink-crypto-udf/target/surefire 2025-07-22T15-55-39_646-jvmRun1 surefire-20250722155539682_1tmp surefire_0-20250722155539682_2tmp"/>
    <property name="http.nonProxyHosts" value="***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Longbridge/flink-crypto-udf/target/test-classes:/Users/<USER>/Longbridge/flink-crypto-udf/target/classes:/Users/<USER>/.m2/repository/org/apache/flink/flink-streaming-java/1.20.1/flink-streaming-java-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-core/1.20.1/flink-core-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-core-api/1.20.1/flink-core-api-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-metrics-core/1.20.1/flink-metrics-core-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-annotations/1.20.1/flink-annotations-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-shaded-asm-9/9.5-17.0/flink-shaded-asm-9-9.5-17.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-shaded-jackson/2.14.2-17.0/flink-shaded-jackson-2.14.2-17.0.jar:/Users/<USER>/.m2/repository/org/snakeyaml/snakeyaml-engine/2.6/snakeyaml-engine-2.6.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.10.0/commons-text-1.10.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/kryo/kryo/2.24.0/kryo-2.24.0.jar:/Users/<USER>/.m2/repository/com/esotericsoftware/minlog/minlog/1.2/minlog-1.2.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/2.1/objenesis-2.1.jar:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.26.0/commons-compress-1.26.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-file-sink-common/1.20.1/flink-file-sink-common-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-runtime/1.20.1/flink-runtime-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-rpc-core/1.20.1/flink-rpc-core-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-rpc-akka-loader/1.20.1/flink-rpc-akka-loader-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-queryable-state-client-java/1.20.1/flink-queryable-state-client-java-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-hadoop-fs/1.20.1/flink-hadoop-fs-1.20.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.15.1/commons-io-2.15.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-shaded-netty/4.1.91.Final-17.0/flink-shaded-netty-4.1.91.Final-17.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-shaded-zookeeper-3/3.7.1-17.0/flink-shaded-zookeeper-3-3.7.1-17.0.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.24.0-GA/javassist-3.24.0-GA.jar:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.10.4/snappy-java-1.1.10.4.jar:/Users/<USER>/.m2/repository/tools/profiler/async-profiler/2.9/async-profiler-2.9.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-java/1.20.1/flink-java-1.20.1.jar:/Users/<USER>/.m2/repository/com/twitter/chill-java/0.7.6/chill-java-0.7.6.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-shaded-guava/31.1-jre-17.0/flink-shaded-guava-31.1-jre-17.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-connector-datagen/1.20.1/flink-connector-datagen-1.20.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-api-java-bridge/1.20.1/flink-table-api-java-bridge-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-api-java/1.20.1/flink-table-api-java-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-api-bridge-base/1.20.1/flink-table-api-bridge-base-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-planner-loader/1.20.1/flink-table-planner-loader-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-clients/1.20.1/flink-clients-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-optimizer/1.20.1/flink-optimizer-1.20.1.jar:/Users/<USER>/.m2/repository/commons-cli/commons-cli/1.5.0/commons-cli-1.5.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-datastream/1.20.1/flink-datastream-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-datastream-api/1.20.1/flink-datastream-api-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-runtime/1.20.1/flink-table-runtime-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-table-common/1.20.1/flink-table-common-1.20.1.jar:/Users/<USER>/.m2/repository/com/ibm/icu/icu4j/67.1/icu4j-67.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-cep/1.20.1/flink-cep-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-cdc-base/3.2.1/flink-cdc-base-3.2.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-cdc-common/3.2.1/flink-cdc-common-3.2.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-cdc-runtime/3.2.1/flink-cdc-runtime-3.2.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-connector-mysql-cdc/3.2.1/flink-connector-mysql-cdc-3.2.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-connector-debezium/3.2.1/flink-connector-debezium-3.2.1.jar:/Users/<USER>/.m2/repository/io/debezium/debezium-api/1.9.8.Final/debezium-api-1.9.8.Final.jar:/Users/<USER>/.m2/repository/io/debezium/debezium-embedded/1.9.8.Final/debezium-embedded-1.9.8.Final.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-api/3.2.0/connect-api-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-clients/3.2.0/kafka-clients-3.2.0.jar:/Users/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-runtime/3.2.0/connect-runtime-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-transforms/3.2.0/connect-transforms-3.2.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-tools/3.2.0/kafka-tools-3.2.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/argparse4j/argparse4j/0.7.0/argparse4j-0.7.0.jar:/Users/<USER>/.m2/repository/ch/qos/reload4j/reload4j/1.2.19/reload4j-1.2.19.jar:/Users/<USER>/.m2/repository/org/bitbucket/b_c/jose4j/0.7.9/jose4j-0.7.9.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.12.6/jackson-annotations-2.12.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/jaxrs/jackson-jaxrs-json-provider/2.12.6/jackson-jaxrs-json-provider-2.12.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/jaxrs/jackson-jaxrs-base/2.12.6/jackson-jaxrs-base-2.12.6.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.12.6/jackson-module-jaxb-annotations-2.12.6.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.2/jakarta.xml.bind-api-2.3.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.1/jakarta.activation-api-1.2.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet/2.34/jersey-container-servlet-2.34.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet-core/2.34/jersey-container-servlet-core-2.34.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/external/jakarta.inject/2.6.1/jakarta.inject-2.6.1.jar:/Users/<USER>/.m2/repository/jakarta/ws/rs/jakarta.ws.rs-api/2.1.6/jakarta.ws.rs-api-2.1.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jersey/inject/jersey-hk2/2.34/jersey-hk2-2.34.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-locator/2.6.1/hk2-locator-2.6.1.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/external/aopalliance-repackaged/2.6.1/aopalliance-repackaged-2.6.1.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-api/2.6.1/hk2-api-2.6.1.jar:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-utils/2.6.1/hk2-utils-2.6.1.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1.1/activation-1.1.1.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.4.44.v20210927/jetty-server-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.4.44.v20210927/jetty-http-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.4.44.v20210927/jetty-io-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.4.44.v20210927/jetty-servlet-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.4.44.v20210927/jetty-security-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util-ajax/9.4.44.v20210927/jetty-util-ajax-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlets/9.4.44.v20210927/jetty-servlets-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-continuation/9.4.44.v20210927/jetty-continuation-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.4.44.v20210927/jetty-util-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/9.4.44.v20210927/jetty-client-9.4.44.v20210927.jar:/Users/<USER>/.m2/repository/org/reflections/reflections/0.9.12/reflections-0.9.12.jar:/Users/<USER>/.m2/repository/org/apache/maven/maven-artifact/3.8.4/maven-artifact-3.8.4.jar:/Users/<USER>/.m2/repository/org/codehaus/plexus/plexus-utils/3.3.0/plexus-utils-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-json/3.2.0/connect-json-3.2.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.12.6/jackson-datatype-jdk8-2.12.6.jar:/Users/<USER>/.m2/repository/org/apache/kafka/connect-file/3.2.0/connect-file-3.2.0.jar:/Users/<USER>/.m2/repository/io/debezium/debezium-connector-mysql/1.9.8.Final/debezium-connector-mysql-1.9.8.Final.jar:/Users/<USER>/.m2/repository/io/debezium/debezium-core/1.9.8.Final/debezium-core-1.9.8.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.2/jackson-core-2.13.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.2.2/jackson-databind-2.13.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.2/jackson-datatype-jsr310-2.13.2.jar:/Users/<USER>/.m2/repository/io/debezium/debezium-ddl-parser/1.9.8.Final/debezium-ddl-parser-1.9.8.Final.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.8/antlr4-runtime-4.8.jar:/Users/<USER>/.m2/repository/com/zendesk/mysql-binlog-connector-java/0.27.2/mysql-binlog-connector-java-0.27.2.jar:/Users/<USER>/.m2/repository/com/github/luben/zstd-jni/1.5.0-2/zstd-jni-1.5.0-2.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.28/mysql-connector-java-8.0.28.jar:/Users/<USER>/.m2/repository/com/esri/geometry/esri-geometry-api/2.2.0/esri-geometry-api-2.2.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-test-utils/1.20.1/flink-test-utils-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-test-utils-junit/1.20.1/flink-test-utils-junit-1.20.1.jar:/Users/<USER>/.m2/repository/org/junit/vintage/junit-vintage-engine/5.10.1/junit-vintage-engine-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.19.1/testcontainers-1.19.1.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.3.3/docker-java-api-3.3.3.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.3.3/docker-java-transport-zerodep-3.3.3.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.3.3/docker-java-transport-3.3.3.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.12.1/jna-5.12.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-runtime/1.20.1/flink-runtime-1.20.1-tests.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-core/1.20.1/flink-core-1.20.1-tests.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-rpc-akka-loader/1.20.1/flink-rpc-akka-loader-1.20.1-tests.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.23.1/assertj-core-3.23.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.12.10/byte-buddy-1.12.10.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-streaming-java/1.20.1/flink-streaming-java-1.20.1-tests.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-statebackend-rocksdb/1.20.1/flink-statebackend-rocksdb-1.20.1.jar:/Users/<USER>/.m2/repository/com/ververica/frocksdbjni/6.20.3-ververica-2.0/frocksdbjni-6.20.3-ververica-2.0.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-statebackend-changelog/1.20.1/flink-statebackend-changelog-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-statebackend-common/1.20.1/flink-statebackend-common-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/flink/flink-dstl-dfs/1.20.1/flink-dstl-dfs-1.20.1.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-test/5.4.0/curator-test-5.4.0.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.7.1/zookeeper-3.7.1.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper-jute/3.7.1/zookeeper-jute-3.7.1.jar:/Users/<USER>/.m2/repository/org/apache/yetus/audience-annotations/0.12.0/audience-annotations-0.12.0.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.76.Final/netty-handler-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.76.Final/netty-common-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.76.Final/netty-resolver-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.76.Final/netty-buffer-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.76.Final/netty-transport-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.76.Final/netty-codec-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.76.Final/netty-transport-native-epoll-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.76.Final/netty-transport-native-unix-common-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.76.Final/netty-transport-classes-epoll-4.1.76.Final.jar:/Users/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/3.2.5/metrics-core-3.2.5.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-01-21"/>
    <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="https.proxyHost" value="127.0.0.1"/>
    <property name="basedir" value="/Users/<USER>/Longbridge/flink-crypto-udf"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Longbridge/flink-crypto-udf/target/surefire/surefirebooter-20250722155539682_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com"/>
    <property name="java.runtime.version" value="23.0.2"/>
    <property name="user.name" value="liujiaxiong"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.2"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Homebrew"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="java.io.tmpdir" value="/var/folders/8s/y6wx6h956892bdz313xnwpxm0000gn/T/"/>
    <property name="java.version" value="23.0.2"/>
    <property name="user.dir" value="/Users/<USER>/Longbridge/flink-crypto-udf"/>
    <property name="os.arch" value="aarch64"/>
    <property name="socksProxyPort" value="7890"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Homebrew"/>
    <property name="java.vm.version" value="23.0.2"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|127.0.0.1|localhost|*.localhost|local|*.local|timestamp.apple.com|*.timestamp.apple.com|sequoia.apple.com|*.sequoia.apple.com|seed-sequoia.siri.apple.com|*.seed-sequoia.siri.apple.com"/>
    <property name="java.class.version" value="67.0"/>
    <property name="http.proxyPort" value="7890"/>
  </properties>
  <testcase name="testEncryptDecrypt" classname="com.longbridge.flink.udf.CryptoUDFTest" time="0.033"/>
  <testcase name="testXorEncodeDecode" classname="com.longbridge.flink.udf.CryptoUDFTest" time="0.001"/>
  <testcase name="testEmptyString" classname="com.longbridge.flink.udf.CryptoUDFTest" time="0.0"/>
  <testcase name="testEncryptDecryptWithCustomKey" classname="com.longbridge.flink.udf.CryptoUDFTest" time="0.0"/>
  <testcase name="testNullInput" classname="com.longbridge.flink.udf.CryptoUDFTest" time="0.0"/>
</testsuite>