#!/bin/bash

# Flink Crypto UDF 测试脚本

echo "=== Flink Crypto UDF 测试脚本 ==="
echo

# 检查 Java 版本
echo "检查 Java 版本..."
java -version
echo

# 编译项目
echo "编译项目..."
mvn compile
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi
echo "✅ 编译成功"
echo

# 运行单元测试
echo "运行单元测试..."
mvn test
if [ $? -ne 0 ]; then
    echo "❌ 单元测试失败"
    exit 1
fi
echo "✅ 单元测试通过"
echo

# 运行功能测试
echo "运行功能测试..."
java -cp target/classes com.longbridge.flink.udf.example.CryptoUtilTest
if [ $? -ne 0 ]; then
    echo "❌ 功能测试失败"
    exit 1
fi
echo "✅ 功能测试通过"
echo

echo "=== 所有测试完成 ==="
